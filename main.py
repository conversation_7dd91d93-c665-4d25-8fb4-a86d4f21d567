from maix import image, display, app, time, camera
import cv2
import numpy as np
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
        # 紫色HSV范围参数
        self.lower_purple = np.array([54, 30, 56])
        self.upper_purple = np.array([230, 178, 255])
        
        # 激光点筛选参数
        self.min_area = 5
        self.max_area = 200
        self.min_circularity = 0.3
        self.min_brightness_v = 150
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 1. 紫色HSV范围检测
        mask_purple = cv2.inRange(hsv, self.lower_purple, self.upper_purple)
        
        # 2. 形态学操作，去除噪点并连接可能的激光点区域
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_OPEN, self.kernel)  # 先开运算去噪
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel) # 再闭运算连接
        
        # 3. 查找轮廓
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        best_laser_point = None
        best_score = -1 # 用于选择最佳点的分数
        
        # 4. 筛选候选激光点
        for cnt in contours_purple:
            area = cv2.contourArea(cnt)
            
            # a. 面积筛选：排除过小或过大的区域
            if not (self.min_area <= area <= self.max_area):
                continue
                
            # b. 圆形度筛选
            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0: continue # 避免除以零
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity < self.min_circularity:
                continue
                
            # c. 计算轮廓中心作为激光点坐标
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            
            # d. 亮度筛选
            v_value_at_center = hsv[cy, cx, 2] # 获取中心点的V值
            if v_value_at_center < self.min_brightness_v:
                continue
                
            # e. 计算评分
            score = v_value_at_center
            # f. 选择最佳点
            if score > best_score:
                best_score = score
                best_laser_point = (cx, cy)
        
        # 5. 如果找到最佳激光点，则绘制并返回
        laser_points = []
        if best_laser_point is not None:
            cx, cy = best_laser_point
            # 绘制激光点标记
            cv2.circle(img, (cx, cy), self.pixel_radius, (255, 0, 255), -1)
            cv2.putText(img, "Purple", (cx - 20, cy - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            laser_points = [best_laser_point]
            
        return img, laser_points

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化显示和摄像头
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    
    # 初始化激光检测器和串口
    laser_detector = PurpleLaserDetector()
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("", "", False)  # 设置帧格式
    else:
        print("串口初始化失败")
        exit()

    # 功能控制变量
    ENABLE_LASER_DETECTION = False  # 控制是否启用激光检测
    
    # 帧处理控制
    FRAME_INTERVAL = 1  # 每隔多少帧检测一次矩形
    
    # 定义参数
    min_contour_area = 3000
    max_contour_area = 45000
    target_sides = 4
    
    # 性能统计
    frame_count = 0
    last_time = time.time()
    fps = 0
    
    # 缓存上一次检测到的内框中心
    last_inner_center = None

    while not app.need_exit():
        frame_count += 1
        # 从摄像头读取图像
        img = cam.read()
        if img is None:
            continue
            
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()
        
        # 获取图像尺寸
        height, width = output.shape[:2]
        
        # 计算屏幕中心点
        center_x = width // 2
        center_y = height // 2

        # 每隔FRAME_INTERVAL帧检测一次矩形
        if frame_count % FRAME_INTERVAL == 0:
            # 1. 矩形检测与处理
            # 转灰度并二值化
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            quads = []
            for cnt in contours:
                area = cv2.contourArea(cnt)
                if min_contour_area < area < max_contour_area:
                    epsilon = 0.03 * cv2.arcLength(cnt, True)
                    approx = cv2.approxPolyDP(cnt, epsilon, True)
                    if len(approx) == target_sides:
                        quads.append((approx, area))

            # 按面积排序（外框在前，内框在后）
            if len(quads) > 1:
                quads.sort(key=lambda x: x[1], reverse=True)
                inner_quads = quads[1:]  # 只处理内框
            else:
                inner_quads = quads  # 如果只有一个四边形，就处理它
            
            # 处理内框
            inner_centers = []
            for approx, area in inner_quads:
                # 计算中心点
                M = cv2.moments(approx)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    inner_centers.append((cx, cy))
                    # 绘制内框轮廓
                    cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
            
            # 更新缓存的内框中心
            if inner_centers:
                last_inner_center = inner_centers[0]  # 只取第一个内框中心
                
                # 发送内框中心点（格式：to:(x,y)）
                x, y = last_inner_center
                # 转换为左下角为原点的坐标
                new_x = x
                new_y = height - y
                center_data = f"to:({new_x},{new_y})"
                micu_printf(center_data)
        
        # 绘制缓存的内框中心
        if last_inner_center:
            cx, cy = last_inner_center
            cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)  # 蓝色中心点
            cv2.putText(output, "Target", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 2. 激光检测处理
        laser_points = []
        if ENABLE_LASER_DETECTION:
            # 如果启用了激光检测，则进行激光检测
            output, laser_points = laser_detector.detect(output)
            
            # 发送激光点（格式：pur:(x,y)）
            if laser_points and len(laser_points) > 0:
                x, y = laser_points[0]
                # 转换为左下角为原点的坐标
                new_x = x
                new_y = height - y
                laser_data = f"pur:({new_x},{new_y})"
                micu_printf(laser_data)
        else:
            # 如果未启用激光检测，则发送屏幕中心点作为紫激光点
            # 绘制十字架标注中心点
            cross_size = 10
            cv2.line(output, (center_x - cross_size, center_y), (center_x + cross_size, center_y), (255, 0, 255), 2)
            cv2.line(output, (center_x, center_y - cross_size), (center_x, center_y + cross_size), (255, 0, 255), 2)
            cv2.circle(output, (center_x, center_y), 3, (255, 0, 255), -1)
            
            # 转换为左下角为原点的坐标
            new_x = center_x
            new_y = height - center_y
            # 发送中心点作为紫激光点
            center_laser_data = f"pur:({new_x},{new_y})"
            micu_printf(center_laser_data)

        # 绘制坐标系原点（左下角）
        origin_x, origin_y = 0, height - 1
        cv2.circle(output, (origin_x, origin_y), 3, (0, 0, 255), -1)
        cv2.putText(output, "(0,0)", (origin_x + 5, origin_y - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

        # 显示FPS和功能状态
        current_time = time.time()
        if current_time - last_time >= 1.0:
            fps = frame_count / (current_time - last_time)
            frame_count = 0
            last_time = current_time
        
        # 显示模式和FPS
        mode_text = "Laser: ON" if ENABLE_LASER_DETECTION else "Laser: OFF"
        cv2.putText(output, f"FPS: {int(fps)} | {mode_text}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)


