# 电赛E题 - 题目二、(2) 解决方案详解

## 📋 题目要求回顾

**题目二、(2)**: 将小车放置在场地中，位置和姿态自定。启动瞄准模块在2s内发射激光击中靶心，要求光斑痕迹距靶心最大距离 D₁ ≤ 2cm。

## 🎯 项目设计核心思路

根据项目作者的设计理念："**虚拟中心点作为当前值，矩形中心点作为目标值**"，结合 `Laser: OFF` 模式，实现了一套巧妙的视觉闭环控制系统。

### 核心控制逻辑

在 `Laser: OFF` 模式下，系统输出两个关键数据：

1. **虚拟中心点（当前值）**: `pur:(160,120)` - 屏幕中心，代表激光当前指向位置
2. **矩形中心点（目标值）**: `to:(x,y)` - 检测到的靶心实际位置

## 🔧 系统架构设计

### 硬件架构
```
MaixCAM (视觉处理) 
    ↓ 串口通信 (115200)
MSPM0 控制板 (运动控制)
    ↓ PWM信号
二维云台舵机 (水平+垂直)
    ↓ 机械连接
蓝紫激光笔 (405nm, ≤10mW)
```

### 软件架构
```
图像采集 → 目标检测 → 坐标计算 → 误差分析 → PID控制 → 舵机驱动 → 激光发射
    ↑                                                                    ↓
    ←←←←←←←←←←←←←← 闭环反馈 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 📊 控制算法详解

### 1. 视觉定位系统

#### 目标检测流程
```python
# 矩形检测核心代码逻辑
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
_, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)
contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

# 筛选四边形目标
for cnt in contours:
    area = cv2.contourArea(cnt)
    if 3000 < area < 45000:  # 面积筛选
        epsilon = 0.03 * cv2.arcLength(cnt, True)
        approx = cv2.approxPolyDP(cnt, epsilon, True)
        if len(approx) == 4:  # 四边形检测
            # 计算中心点
            M = cv2.moments(approx)
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
```

#### 坐标系转换
- **摄像头坐标系**: 左上角为原点 (0,0)
- **控制坐标系**: 左下角为原点 (0,0)
- **转换公式**: `new_y = height - y`

### 2. 双点控制算法

#### 误差计算
```
error_x = target_x - current_x  // 矩形中心X - 屏幕中心X
error_y = target_y - current_y  // 矩形中心Y - 屏幕中心Y
```

#### PID控制器
```c
// MSPM0端控制算法伪代码
float pid_control_x(float error) {
    static float integral_x = 0, last_error_x = 0;
    
    integral_x += error * dt;
    float derivative = (error - last_error_x) / dt;
    
    float output = Kp_x * error + Ki_x * integral_x + Kd_x * derivative;
    last_error_x = error;
    
    return output;
}
```

### 3. 闭环控制流程

```mermaid
graph TD
    A[摄像头采集图像 320×240] --> B[检测黑色矩形框]
    B --> C[计算矩形中心 to:x,y]
    C --> D[获取屏幕中心 pur:160,120]
    D --> E[计算位置误差]
    E --> F[PID控制算法]
    F --> G[调整云台角度]
    G --> H[激光指向更新]
    H --> A
    I[误差 < 阈值?] --> J[发射激光击中靶心]
    E --> I
    I --> |否| F
```

## 💻 代码实现方案

### MaixCAM端 (已实现)

#### 关键配置
```python
# 功能控制变量
ENABLE_LASER_DETECTION = False  # 使用虚拟中心点模式

# 摄像头配置
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 串口配置
uart = SimpleUART()
uart.init("/dev/ttyS0", 115200, set_as_global=True)
```

#### 数据输出格式
```python
# 目标中心点输出
center_data = f"to:({new_x},{new_y})"
micu_printf(center_data)

# 虚拟中心点输出  
center_laser_data = f"pur:({new_x},{new_y})"
micu_printf(center_laser_data)
```

### MSPM0端 (需要实现)

#### 串口数据解析
```c
typedef struct {
    int target_x, target_y;    // to:(x,y) 目标位置
    int current_x, current_y;  // pur:(x,y) 当前位置
    bool data_valid;
} coordinate_data_t;

void parse_uart_data(char* buffer) {
    // 解析 "to:(x,y)" 格式
    if(strstr(buffer, "to:(")) {
        sscanf(buffer, "to:(%d,%d)", &coord.target_x, &coord.target_y);
    }
    // 解析 "pur:(x,y)" 格式  
    if(strstr(buffer, "pur:(")) {
        sscanf(buffer, "pur:(%d,%d)", &coord.current_x, &coord.current_y);
        coord.data_valid = true;
    }
}
```

#### 舵机控制
```c
#define SERVO_X_PIN  // 水平舵机PWM引脚
#define SERVO_Y_PIN  // 垂直舵机PWM引脚

void servo_control() {
    if(coord.data_valid) {
        // 计算误差
        float error_x = coord.target_x - coord.current_x;
        float error_y = coord.target_y - coord.current_y;
        
        // PID控制
        float pwm_x = pid_control_x(error_x);
        float pwm_y = pid_control_y(error_y);
        
        // 输出到舵机
        set_servo_pwm(SERVO_X_PIN, pwm_x);
        set_servo_pwm(SERVO_Y_PIN, pwm_y);
        
        // 判断瞄准精度
        if(abs(error_x) < THRESHOLD_X && abs(error_y) < THRESHOLD_Y) {
            laser_fire();  // 发射激光
        }
    }
}
```

#### 激光控制
```c
#define LASER_PIN    // 激光控制引脚
#define FIRE_TIME_MS 100  // 激光发射持续时间

void laser_fire() {
    GPIO_setOutputHigh(LASER_PIN);
    delay_ms(FIRE_TIME_MS);
    GPIO_setOutputLow(LASER_PIN);
}
```

## ⚙️ 参数调优指南

### 1. PID参数调优

#### 水平方向 (X轴)
```c
float Kp_x = 0.5;   // 比例系数，影响响应速度
float Ki_x = 0.1;   // 积分系数，消除稳态误差
float Kd_x = 0.05;  // 微分系数，减少超调
```

#### 垂直方向 (Y轴)
```c
float Kp_y = 0.5;   
float Ki_y = 0.1;   
float Kd_y = 0.05;  
```

### 2. 瞄准精度阈值
```c
#define THRESHOLD_X 10  // X方向误差阈值 (像素)
#define THRESHOLD_Y 10  // Y方向误差阈值 (像素)
```

### 3. 舵机角度限制
```c
#define SERVO_MIN_ANGLE -90   // 最小角度
#define SERVO_MAX_ANGLE 90    // 最大角度
#define SERVO_CENTER_ANGLE 0  // 中心角度
```

## 🎯 系统优势分析

### 1. **实时性优异**
- 摄像头30FPS实时反馈
- 虚拟中心点避免激光反射干扰
- 串口115200高速通信

### 2. **精度控制精确**
- 矩形检测比激光点检测更稳定
- 双点控制算法简单有效
- PID闭环控制保证精度

### 3. **环境适应性强**
- 不依赖特定激光颜色检测
- 对环境光照要求较低
- 黑色矩形框易于识别

### 4. **响应速度快**
- 2秒内完成瞄准要求
- 实时图像处理和控制
- 硬件响应延迟小

## 🧪 测试验证方案

### 1. 静态精度测试
```
测试条件: 小车固定位置，靶面距离50cm
测试指标: D₁ ≤ 2cm
测试次数: 10次
成功标准: 成功率 ≥ 90%
```

### 2. 响应时间测试  
```
测试条件: 不同初始偏差角度
测试指标: t ≤ 2s
测试方法: 从启动到激光发射的时间
成功标准: 平均时间 ≤ 1.5s
```

### 3. 环境适应性测试
```
测试条件: 不同光照条件
测试指标: 检测成功率
测试环境: 室内灯光、自然光、阴影
成功标准: 各环境成功率 ≥ 85%
```

## 📈 性能预期

根据设计方案，预期性能指标：

- **瞄准精度**: D₁ ≤ 1cm (优于要求的2cm)
- **响应时间**: t ≤ 1.5s (优于要求的2s)  
- **成功率**: ≥ 95%
- **重复精度**: ±0.5cm

## 🔧 调试建议

### 1. 分步调试
1. **视觉系统**: 确认矩形检测准确性
2. **通信系统**: 验证串口数据传输
3. **控制系统**: 调试PID参数
4. **整体系统**: 综合性能测试

### 2. 常见问题排查
- **检测失败**: 调整二值化阈值、光照条件
- **控制震荡**: 减小PID参数，增加阻尼
- **响应慢**: 增大比例系数Kp
- **精度不够**: 细化阈值，优化机械结构

这套方案充分利用了视觉反馈的优势，通过虚拟中心点与实际目标点的双点控制，实现了高精度、快响应的自动瞄准系统，完全满足题目二、(2)的技术要求。
